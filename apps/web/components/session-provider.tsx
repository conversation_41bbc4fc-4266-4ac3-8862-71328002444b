'use client';

import { createContext, SetStateAction, useCallback, useContext, useEffect, useState } from 'react';
import type { User } from '@supabase/supabase-js';
import { useCaptchaWrapperContext } from '@pdfily/auth/captcha/client';
import authConfig from '@pdfily/config/auth.config';
import { useSupabase } from '@pdfily/supabase/hooks/use-supabase';
import { useAnonymous } from './anonymous-provider';

/**
 * @interface SessionContextType
 *
 * Represents the authentication session context.
 *
 * @property {User | null} user - The authenticated user object or null if not authenticated.
 * @property {boolean} isAuthenticated - Indicates if the user is authenticated.
 * @property {boolean} isAnonymous - Indicates if the user is authenticated anonymously.
 * @property {boolean} isLoading - Indicates if the session is loading.
 */
interface SessionContextType {
  user: User | null;
  setUser: React.Dispatch<SetStateAction<User | null>>;
  isAuthenticated: boolean;
  setIsAuthenticated: React.Dispatch<SetStateAction<boolean>>;
  isAnonymous: boolean;
  setIsAnonymous: React.Dispatch<SetStateAction<boolean>>;
  isLoading: boolean;
}

/**
 * @name SessionContext
 *
 * @description
 * The `SessionContext` is a React context that holds the authentication session data.
 */
const SessionContext = createContext<SessionContextType | undefined>(undefined);

/**
 * @name useAuth
 *
 * Custom hook to access the authentication context.
 *
 * @returns {SessionContextType} - The current authentication context.
 */
export function useAuth(): SessionContextType {
  const context = useContext(SessionContext);

  if (!context) {
    throw new Error('useAuth must be used within a SessionProvider');
  }

  return context;
}

/**
 * Extracts the project reference (project_ref) from a given URL.
 *
 * Assumes that the project reference is the first subdomain in the hostname.
 *
 * @param url - The full URL string to extract the project reference from
 * @returns The project reference as a string, or undefined if extraction fails
 */
const getProjectRefFromUrl = (url: string): string | undefined => {
  try {
    const hostname = new URL(url).hostname;
    const projectRef = hostname.split('.')[0];
    return projectRef;
  } catch (err) {
    console.error('[Auth] Failed to extract project_ref from URL:', err);
    return undefined;
  }
};

/**
 * Retrieves the authenticated user from a Supabase auth cookie stored in the browser.
 *
 * - Works only in a client-side environment.
 * - Extracts and decodes the auth token from the cookie.
 * - Verifies the token's expiration before returning the user.
 *
 * @param projectRef - The Supabase project reference used to identify the auth cookie
 * @returns The authenticated user object if valid, otherwise undefined
 */

const getUserFromCookie = (projectRef: string | undefined): User | undefined => {
  // Check it's a client-side environment
  if (typeof window === 'undefined') {
    return;
  }

  if (!projectRef) return undefined;

  try {
    const cookies = window.document.cookie.split('; ');
    const authCookiePrefix = `sb-${projectRef}-auth-token`;

    // Look for both single cookie and chunked cookies
    const authCookies = cookies.filter(
      (cookie) => cookie.startsWith(`${authCookiePrefix}=base64-`) || cookie.startsWith(`${authCookiePrefix}.`),
    );

    if (authCookies.length === 0) {
      console.warn('[Auth] No auth cookie found');
      return undefined;
    }

    let cookieValue: string | undefined;

    // Handle single cookie case
    const singleCookie = authCookies.find((cookie) => cookie.startsWith(`${authCookiePrefix}=base64-`));
    if (singleCookie) {
      cookieValue = singleCookie.split('=')[1];
    } else {
      // Handle chunked cookies case
      const chunkedCookies = authCookies
        .filter((cookie) => cookie.startsWith(`${authCookiePrefix}.`))
        .map((cookie) => {
          const [key, value] = cookie.split('=');
          const chunkIndex = parseInt(key?.split('.')[1]!, 10);
          return { index: chunkIndex, value };
        })
        .sort((a, b) => a.index - b.index); // Sort by chunk index

      if (chunkedCookies.length === 0) {
        console.warn('[Auth] No chunked auth cookies found');
        return undefined;
      }

      // Reconstruct the full cookie value from chunks
      cookieValue = chunkedCookies.map((chunk) => chunk.value).join('');
    }

    // Remove the "base64-" prefix if present
    const cleanedCookie = cookieValue?.startsWith('base64-') ? cookieValue.replace('base64-', '') : cookieValue;

    // Decode the Base64 string
    const decodedJson = atob(cleanedCookie!);

    // Parse the JSON
    const tokenData = JSON.parse(decodedJson);
    const { user, expires_at } = tokenData;

    // Check if the token is still valid
    if (expires_at * 1000 < Date.now()) {
      console.warn('[Auth] Token in cookie is expired');
      return;
    }

    if (!user) {
      console.warn('[Auth] No user data in cookie');
      return;
    }

    return user as User;
  } catch (err) {
    console.error('[Auth] Failed to parse auth cookie:', err);
    return;
  }
};

/**
 * @name SessionProvider
 *
 * React component to provide the session context to its children.
 *
 * @param {React.PropsWithChildren} props - The provider props containing child components.
 */
export function SessionProvider({ children }: React.PropsWithChildren) {
  const supabase = useSupabase();
  const { captchaToken } = useCaptchaWrapperContext();
  const { signInAnonymously } = useAnonymous();

  // Extract the project ref from the URL
  const projectRef = getProjectRefFromUrl(process.env.NEXT_PUBLIC_SUPABASE_URL || '');

  // State variables
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isAnonymous, setIsAnonymous] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Preload the user from the cookie on client-side mount
  useEffect(() => {
    const preloadUser = () => {
      const preloadedUser = getUserFromCookie(projectRef!);
      if (preloadedUser) {
        setUser(preloadedUser);
        setIsAuthenticated(!preloadedUser.is_anonymous);
        setIsAnonymous(!!preloadedUser.is_anonymous);
        setIsLoading(false);
      } else {
        setIsLoading(true); // Continue loading if no user was preloaded
      }
    };

    preloadUser();
  }, [projectRef]);

  /**
   * @name getUser
   *
   * @description
   * Retrieves the current authenticated user from Supabase and updates the auth state.
   */
  const getUser = useCallback(async () => {
    setIsLoading(true);

    try {
      const { data, error } = await supabase.auth.getUser();

      if (error || !data?.user) {
        console.warn('[Auth] No authenticated user found. Initiating anonymous sign-in.');
        await signInAnonymously();
        return;
      }

      const fetchedUser = data.user;

      setUser(fetchedUser);
      setIsAuthenticated(!fetchedUser.is_anonymous);
      setIsAnonymous(!!fetchedUser.is_anonymous);
    } catch (err) {
      console.error('[Auth] Failed to fetch user or sign in anonymously:', err);
    } finally {
      setIsLoading(false);
    }
  }, [supabase, signInAnonymously]);

  // Check the user if no user was preloaded
  useEffect(() => {
    const captchaDisabled = !authConfig.captchaEnabled;
    const captchaPassed = authConfig.captchaEnabled && captchaToken;

    if (!user && (captchaDisabled || captchaPassed)) {
      getUser();
    }
  }, [user, captchaToken, getUser]);

  return (
    <SessionContext.Provider
      value={{
        user,
        setUser,
        isAuthenticated,
        setIsAuthenticated,
        isAnonymous,
        setIsAnonymous,
        isLoading,
      }}
    >
      {children}
    </SessionContext.Provider>
  );
}
