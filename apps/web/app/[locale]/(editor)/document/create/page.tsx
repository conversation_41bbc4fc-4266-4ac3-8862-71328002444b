'use client';

import { useState, useEffect } from 'react';
import { useLocale } from 'next-intl';
import { PDFViewerRendererContainer } from '@pdfily/documents/components';
import { useDocument } from '@pdfily/documents/hooks/use-document';
import { useLastEditFileId } from '@pdfily/documents/hooks/use-last-edit-file-id';
import { useAuth } from '@/components/session-provider';
import { useRouter } from '@/lib/i18n/navigation';

/**
 * Document create page
 */
export default function Page() {
  const router = useRouter();
  const locale = useLocale();
  const { user, isLoading: isAuthLoading } = useAuth();
  const { lastEditFileId: documentId } = useLastEditFileId();
  const [source, setSource] = useState<string | null>(null);
  const [authTimeout, setAuthTimeout] = useState(false);
  const { document, error, isLoading: isDocumentLoading } = useDocument(documentId);

  // Set a timeout for auth to complete
  useEffect(() => {
    const timer = setTimeout(() => {
      setAuthTimeout(true);
    }, 5000); // 5 seconds timeout for auth

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Wait for auth to complete
    if (isAuthLoading) return;

    // If no documentId after auth is done, redirect
    if (!documentId) {
      router.push('/');
      return;
    }

    if (error) {
      router.push('/');
      return;
    }

    if (document) {
      setSource(document.edited_file_url ?? document.original_file_url);
    }
  }, [isAuthLoading, documentId, document, error, router]);

  // Show a skeleton loader if authentication or document is still loading
  if (isAuthLoading || isDocumentLoading || !source) {
    return (
      <div className="flex items-center justify-center h-screen flex-col gap-y-2">
        <div className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-4">Preparing your file</p>
      </div>
    );
  }

  // Show a message if no user is authenticated, but only wait up to the timeout
  if (!user && !authTimeout) {
    return (
      <div className="flex items-center justify-center h-screen flex-col gap-y-2">
        <div className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-4">Loading user session...</p>
      </div>
    );
  }

  // If timeout reached and still no user, proceed anyway (for anonymous access)
  if (!user && authTimeout) {
    console.warn('Auth timeout reached, proceeding without user');
    // You might want to redirect or show an error here
    router.push('/');
    return null;
  }

  return (
    <div className="flex w-full flex-col">
      <PDFViewerRendererContainer source={source} documentId={documentId} locale={locale} user={user} />
    </div>
  );
}
