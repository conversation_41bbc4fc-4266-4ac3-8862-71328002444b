'use client';

import { useState, useEffect } from 'react';
import { useLocale } from 'next-intl';
import { PDFViewerRendererContainer } from '@pdfily/documents/components';
import { useDocument } from '@pdfily/documents/hooks/use-document';
import { useLastEditFileId } from '@pdfily/documents/hooks/use-last-edit-file-id';
import { useAuth } from '@/components/session-provider';
import { useRouter } from '@/lib/i18n/navigation';

/**
 * Document create page
 */
export default function Page() {
  const router = useRouter();
  const locale = useLocale();
  const { user, isLoading: isAuthLoading } = useAuth();
  const { lastEditFileId: documentId } = useLastEditFileId();
  const [source, setSource] = useState<string | null>(null);
  const { document, error, isLoading: isDocumentLoading } = useDocument(documentId);

  useEffect(() => {
    console.log('[DEBUG] useEffect called:', {
      isAuthLoading,
      documentId,
      hasDocument: !!document,
      hasError: !!error,
      currentSource: source,
    });

    // Wait for auth to complete
    if (isAuthLoading) {
      console.log('[DEBUG] Auth still loading...');
      return;
    }

    // If no documentId after auth is done, redirect
    if (!documentId) {
      console.log('[DEBUG] REDIRECTING: No documentId');
      router.push('/');
      return;
    }

    if (error) {
      console.log('[DEBUG] REDIRECTING: Error found:', error);
      router.push('/');
      return;
    }

    if (document) {
      const newSource = document.edited_file_url ?? document.original_file_url;
      console.log('[DEBUG] Setting source:', newSource);
      setSource(newSource);
    } else {
      console.log('[DEBUG] Document not ready yet');
    }
  }, [isAuthLoading, documentId, document, error, router, source]);

  console.log('[DEBUG] Render conditions:', {
    isAuthLoading,
    isDocumentLoading,
    hasSource: !!source,
    hasUser: !!user,
  });

  // Show a skeleton loader if authentication or document is still loading
  if (isAuthLoading || isDocumentLoading || !source) {
    console.log('[DEBUG] Showing loader');
    return (
      <div className="flex items-center justify-center h-screen flex-col gap-y-2">
        <div className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-4">Preparing your file</p>
      </div>
    );
  }

  // Show a message if no user is authenticated
  if (!user) {
    console.log('[DEBUG] No user, showing user loader');
    return (
      <div className="flex items-center justify-center h-screen flex-col gap-y-2">
        <div className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-4">Loading user session...</p>
      </div>
    );
  }

  console.log('[DEBUG] Rendering PDF viewer');
  return (
    <div className="flex w-full flex-col">
      <PDFViewerRendererContainer source={source} documentId={documentId} locale={locale} user={user} />
    </div>
  );
}
