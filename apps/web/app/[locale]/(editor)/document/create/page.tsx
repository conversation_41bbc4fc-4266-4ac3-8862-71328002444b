'use client';

import { useState, useEffect } from 'react';
import { useLocale } from 'next-intl';
import { PDFViewerRendererContainer } from '@pdfily/documents/components';
import { useDocument } from '@pdfily/documents/hooks/use-document';
import { useLastEditFileId } from '@pdfily/documents/hooks/use-last-edit-file-id';
import { useAuth } from '@/components/session-provider';
import { useRouter } from '@/lib/i18n/navigation';

/**
 * Document create page
 */
export default function Page() {
  const router = useRouter();
  const locale = useLocale();
  const { user, isLoading: isAuthLoading } = useAuth();
  const { lastEditFileId: documentId } = useLastEditFileId();
  const [source, setSource] = useState<string | null>(null);
  const { document, error, isLoading: isDocumentLoading } = useDocument(documentId);

  useEffect(() => {
    console.log('[DEBUG] useEffect triggered:', {
      isAuthLoading,
      documentId,
      isDocumentLoading,
      hasDocument: !!document,
      hasError: !!error,
      source,
    });

    // Wait for auth to finish loading
    if (isAuthLoading) {
      console.log('[DEBUG] Auth still loading, waiting...');
      return;
    }

    // Wait a bit more for documentId to be available from localStorage
    if (!documentId && !isDocumentLoading) {
      console.log('[DEBUG] REDIRECTING: No documentId and not loading document');
      router.push('/');
      return;
    }

    if (error) {
      console.log('[DEBUG] REDIRECTING: Error found:', error);
      router.push('/');
      return;
    }

    if (document) {
      const newSource = document.edited_file_url ?? document.original_file_url;
      console.log('[DEBUG] Setting source:', newSource);
      setSource(newSource);
    } else {
      console.log('[DEBUG] Document not ready yet');
    }
  }, [isAuthLoading, documentId, document, error, isDocumentLoading, router, source]);

  // Show a skeleton loader if authentication or document is still loading
  if (isAuthLoading || isDocumentLoading || !source) {
    return (
      <div className="flex items-center justify-center h-screen flex-col gap-y-2">
        <div className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-4">Preparing your files</p>
      </div>
    );
  }

  // Show a message if no user is authenticated
  if (!user) {
    return (
      <div className="flex items-center justify-center h-screen flex-col gap-y-2">
        <div className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-4">Preparing your file</p>
      </div>
    );
  }

  return (
    <div className="flex w-full flex-col">
      <PDFViewerRendererContainer source={source} documentId={documentId} locale={locale} user={user} />
    </div>
  );
}
