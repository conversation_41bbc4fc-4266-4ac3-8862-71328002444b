import { useMemo } from 'react';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import s3Config from '@pdfily/config/s3-config';
import { getFileType, getInternalFileType, getPdfPageCount, sanitizeFileName } from '@pdfily/shared/utils';

import { useSupabase } from './use-supabase';
import { getPresignedUploadUrl } from '../lib/s3/get-presigned-upload-url';
import { Database } from '../database.types';

type DocumentInsert = Database['public']['Tables']['documents']['Insert'];

/**
 * Represents a structured error response object.
 *
 * @interface ErrorResponse
 */
interface UploadErrorResponse {
  error: any;
  message: string;
  statusCode: number;
  timestamp: string;
}

interface UploadSuccessResponse {
  documentId: string;
  file: {
    file_name: string;
    file_key: string;
    file_size: number | null | undefined;
    file_url: string | null;
  };
}

type UploadResponse = UploadSuccessResponse | UploadErrorResponse;

interface UploadFileOptionsProps {
  setProgress?: (percent: number) => void;
  setDocumentId?: (documentId: string) => void;
  onSuccess?: (success: UploadSuccessResponse) => void;
  onError?: (error: UploadErrorResponse) => void;
}

/**
 * React hook to handle file uploads to Supabase S3 using a presigned URL,
 * including progress tracking and metadata saving.
 *
 * @param setProgress - Callback to update the upload progress (0–100).
 * @returns uploadFile - A memoized async function to upload a single file.
 */
export function useUploadFile({ setProgress, setDocumentId, onSuccess, onError }: UploadFileOptionsProps) {
  const supabase = useSupabase();

  const uploadFile = useMemo(
    () =>
      async (file: File | undefined): Promise<UploadResponse> => {
        if (!file) {
          const errorResponse = {
            message: 'No file selected. Missing file during upload.',
            error: 'Unknown error',
            statusCode: 400,
            timestamp: new Date().toISOString(),
          };

          onError?.(errorResponse);
          return errorResponse;
        }

        // Get user details if authenticated ===
        const { data: { user } = {} } = await supabase.auth.getUser();

        if (!user) {
          const errorResponse = {
            message: 'Failed to upload and save document',
            error: 'User not authenticated',
            statusCode: 401,
            timestamp: new Date().toISOString(),
          };

          onError?.(errorResponse);

          return errorResponse;
        }

        try {
          const bucket = s3Config.bucket ?? 'uploads';
          const documentId = uuidv4();
          const sanitizedFileName = sanitizeFileName(file.name);
          const fileKey = `documents/${documentId}/${Date.now()}-${sanitizedFileName}`;

          // Generate signed file URL ===
          const preSignedUrl = await getPresignedUploadUrl(bucket, fileKey);

          if (!preSignedUrl) {
            const errorResponse = {
              message: 'Failed to upload and save document',
              error: 'Failed to generate pre-signed URL',
              statusCode: 500,
              timestamp: new Date().toISOString(),
            };

            onError?.(errorResponse);
            return errorResponse;
          }

          // Upload to S3 using pre-signed URL and Axios ===
          const uploadResponse = await axios.put(preSignedUrl, file, {
            headers: {
              'Content-Type': file.type,
              'Cache-Control': 'max-age=3600',
              Accept: 'application/json',
            },
            onUploadProgress: (event) => {
              if (setProgress) {
                const percent = Math.round((event.loaded * 100) / (event.total || file.size));
                setProgress(percent);
              }
            },
          });

          if (uploadResponse.status !== 200) {
            const errorResponse = {
              message: 'Failed to upload file to S3',
              error: uploadResponse.statusText,
              statusCode: 500,
              timestamp: new Date().toISOString(),
            };

            onError?.(errorResponse);
            return errorResponse;
          }

          // Extract metadata ===
          const pageCount = await getPdfPageCount(file);
          const fileType = getFileType(file.type);
          const internalType = getInternalFileType(file.name);

          // Prepare metadata for inserting into DB
          const metadata: DocumentInsert = {
            id: documentId,
            title: file.name,
            user_id: user?.id,
            original_key: fileKey,
            name: file.name,
            size: file.size,
            type: fileType,
            format: fileType.toUpperCase(),
            page_count: pageCount,
            internal_type: internalType,
            last_modified: new Date().toISOString(),
          };

          // Save metadata to Supabase DB ===
          const { data: dbData, error } = await supabase.from('documents').insert([metadata]).select('*');

          if (error || !dbData?.[0]) {
            const errorResponse = {
              message: 'Failed to save document metadata.',
              error: error?.message,
              statusCode: 500,
              timestamp: new Date().toISOString(),
            };

            onError?.(errorResponse);
            return errorResponse;
          }

          const response: UploadSuccessResponse = {
            documentId: dbData[0]?.id,
            file: {
              file_name: metadata.name,
              file_key: metadata.original_key,
              file_size: metadata.size,
              file_url: preSignedUrl,
            },
          };

          if (setDocumentId) {
            console.log('[useUploadFile] Setting documentId:', response.documentId);
            setDocumentId?.(response.documentId);
          }

          if (onSuccess) {
            onSuccess?.(response);
          }

          return response;
        } catch (error: any) {
          const errorResponse = {
            message: 'Failed to upload and save document',
            error: error?.message,
            statusCode: 500,
            timestamp: new Date().toISOString(),
          };

          onError?.(errorResponse);
          return errorResponse;
        }
      },
    [supabase, setProgress, setDocumentId, onSuccess, onError],
  );

  return uploadFile;
}
