'use client';

import { isBrowser, LocalStorageManager } from '@pdfily/shared/utils';
import { useCallback, useEffect, useState } from 'react';

// configure this as you wish
const LAST_EDIT_FILE_ID_KEY = 'last-edit-file-id';

export function useLastEditFileId() {
  const [lastEditFileId, setLastEditFileId] = useState<string | null>(null);

  // Run only on the client
  useEffect(() => {
    const storedFileId = getLastEditFileIdFromLocalStorage();
    console.log('[useLastEditFileId] Retrieved from localStorage:', storedFileId);
    setLastEditFileId(storedFileId);
  }, []);

  const setFileId = useCallback((fileId: string) => {
    console.log('[useLastEditFileId] Setting fileId:', fileId);
    setLastEditFileId(fileId);
    storeLastEditFileIdInLocalStorage(fileId);
  }, []);

  const clearFileId = useCallback(() => {
    setLastEditFileId(null);
    removeLastEditFileIdFromLocalStorage();
  }, []);

  return {
    lastEditFileId,
    setFileId,
    clearFileId,
  };
}

/**
 * Stores the given file ID in localStorage.
 *
 * This function ensures that localStorage is accessed only in the browser environment.
 * If the provided file ID is `null` or `undefined`, it removes the key from localStorage.
 *
 * @param fileId - The file ID to store in localStorage.
 */
function storeLastEditFileIdInLocalStorage(fileId: string | null) {
  if (!isBrowser()) {
    return;
  }

  if (fileId) {
    LocalStorageManager.setItem(LAST_EDIT_FILE_ID_KEY, fileId);
  }
}

/**
 * Retrieves the last edited file ID from localStorage.
 *
 * This function ensures that localStorage is accessed only in the browser environment.
 * If the key is not found in localStorage, it returns `null`.
 *
 * @returns {string | null} The last edited file ID if found, otherwise `null`.
 */
function getLastEditFileIdFromLocalStorage(): string | null {
  if (!isBrowser()) {
    return null;
  }

  return LocalStorageManager.getItem(LAST_EDIT_FILE_ID_KEY);
}

/**
 * Removes the last edited file ID from localStorage.
 *
 * This function ensures that localStorage is accessed only in the browser environment.
 * If the key does not exist in localStorage, the function performs no action.
 *
 * @returns {void} This function does not return any value.
 */
function removeLastEditFileIdFromLocalStorage(): void {
  if (!isBrowser()) {
    return;
  }

  LocalStorageManager.removeItem(LAST_EDIT_FILE_ID_KEY);
}
