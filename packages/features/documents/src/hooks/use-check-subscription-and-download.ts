import { useRouter } from 'next/navigation';
import { useCallback } from 'react';
import Swal from 'sweetalert2';
import type { User } from '@supabase/supabase-js';
import { useCaptchaWrapperContext } from '@pdfily/auth/captcha/client';
import { UploadSuccessResponse } from '@pdfily/supabase/hooks/use-upload-file-by-document';
import { useSupabase } from '@pdfily/supabase/hooks/use-supabase';
import { downloadBlob, generateRandomPassword } from '@pdfily/shared/utils';
import authConfig from '@pdfily/config/auth.config';
import { signInWithProviderAction } from '@pdfily/auth/signin';

/**
 * Centralized hook to handle download logic
 * Includes handling for both anonymous and authenticated users
 */
export function useDownloadLogic(user: User) {
  const router = useRouter();
  const supabase = useSupabase();
  // ⚠️ IMPORTANT: Retrieve the captcha context, not just the token
  const captchaContext = useCaptchaWrapperContext();

  /**
   * Handles the download of a document.
   */
  const documentDownload = useCallback(async (documentId: string, documentName: string) => {
    try {
      const response = await fetch(`/api/v1/files/${encodeURIComponent(documentId)}/download`, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
        },
      });

      if (!response.ok) {
        const { error, message } = await response.json();
        Swal.fire({
          icon: 'error',
          title: error ?? 'Oops...',
          text: message ?? 'Something went wrong while downloading the file.',
        });
        return;
      }

      const blob = await response.blob();
      downloadBlob(blob, response, documentName);
    } catch (error) {
      console.log({ error });
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'Something went wrong while downloading the document.',
      });
    }
  }, []);

  /**
   * Check the subscription and proceed with download
   */
  const checkSubscriptionAndDownload = useCallback(
    async (documentId: string, fileName: string) => {
      const { data } = await supabase.rpc('has_subscription_status', {
        user_id: user.id,
      });

      const subscription = data?.[0];
      const hasSubscription = (subscription?.has_subscription || user.user_metadata.is_subscribed) ?? false;

      if (!hasSubscription) {
        router.push('/checkout');
        return;
      }

      await documentDownload(documentId, fileName);
    },
    [user.id, user.user_metadata.is_subscribed, supabase, router, documentDownload],
  );

  /**
   * Handles the email modal for anon users
   */
  const handleEmailModal = useCallback(
    async (documentId: string, uploadResponse: UploadSuccessResponse) => {
      const { googleAuthEnabled } = authConfig;

      // Refresh captcha token
      const captchaTokenPromise = captchaContext.refreshCaptchaToken().catch(() => null);
      await Swal.fire({
        customClass: {
          input: 'swal2-input-email rounded-[10px]',
          inputLabel: 'font-normal text-[14px] leading-[20px] space-y-4 text-[#1C1C1C] swal2-label-inline-flex',
          confirmButton:
            'bg-[#F0401D] text-white py-4 px-8 rounded-[10px] text-[16px] leading-[20px] font-medium swal2-email-validation-btn',
          validationMessage: 'swal2-validation-message',
          footer: 'border-none',
        },
        icon: undefined,
        html: `
        <img src="/images/apryse/check.svg" style="width: 56px; margin-bottom: 22px; margin-left: auto; margin-right: auto;" />
        <div style="font-size: 24px; font-weight: 500; color: #1C1C1C; text-align: center; margin-bottom: 16px;">
          Use your email or Google account to continue
        </div>
        <!-- Google Button - Only show if enabled -->
        ${
          googleAuthEnabled
            ? `
          <div id="google-auth-container" style="margin-bottom: 24px;">
            <button 
              id="popup-google-sign-in-btn" 
              type="button"
              style="
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
                width: 100%;
                padding: 12px 16px;
                border: 1px solid #e5e7eb;
                border-radius: 10px;
                background: white;
                font-size: 16px;
                font-weight: 500;
                color: #374151;
                cursor: pointer;
                transition: all 0.2s;
              "
              onmouseover="this.style.backgroundColor='#f9fafb'; this.style.borderColor='#d1d5db'; this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'"
              onmouseout="this.style.backgroundColor='white'; this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'"
            >
              <img src="/images/auth/google.svg" alt="Google" style="width: 20px; height: 20px;" />
              Continue with Google
            </button>
            
            <div style="display: flex; align-items: center; margin: 16px 0;">
              <div style="flex-grow: 1; height: 1px; background-color: #d1d5db;"></div>
              <span style="padding: 0 16px; font-size: 14px; color: #6b7280;">OR</span>
              <div style="flex-grow: 1; height: 1px; background-color: #d1d5db;"></div>
            </div>
          </div>
        `
            : ''
        }
      `,
        showCloseButton: true,
        buttonsStyling: false,
        confirmButtonText: 'Download',
        showLoaderOnConfirm: true,
        input: 'email',
        inputLabel: 'Email address',
        inputPlaceholder: 'Enter email address',
        footer: `
        <div style="font-size: 14px; line-height: 20px; color: #585858;" class="text-center flex flex-col items-center space-y-4 px-8">
          <p style="font-size: 16px; line-height: 22px;">Already have an account? <a href="/auth/sign-in" style="color: #F0401D;">Sign In</a></p>
          <p>
            By clicking <strong>Download</strong>, you agree to the
            <a href="/terms-conditions" style="text-decoration: underline;">Terms of Service</a>,
            <a href="/terms-conditions" style="text-decoration: underline;">Subscription Terms</a>,
            <a href="/privacy-policy" style="text-decoration: underline;">Privacy Policy</a>, and
            <a href="/privacy-policy" style="text-decoration: underline;">Cookie Policy</a>.
          </p>
        </div>
      `,
        inputValidator: (value) => {
          if (!value) {
            return 'Email must be specified';
          }
        },
        didOpen: () => {
          // Attach Google sign-in handler after modal opens (only if enabled)
          if (googleAuthEnabled) {
            const googleBtn = document.getElementById('popup-google-sign-in-btn');
            if (googleBtn) {
              googleBtn.addEventListener('click', async () => {
                try {
                  // Close current modal
                  Swal.close();

                  // Handle Google sign-in
                  await handleGoogleSignIn(documentId, uploadResponse);
                } catch (error) {
                  console.error('Google sign-in error:', error);
                  Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to sign in with Google. Please try again.',
                  });
                }
              });
            }
          }
        },
        preConfirm: async (email) => {
          if (!email) return false;

          try {
            const captchaToken = await captchaTokenPromise;
            const response = await fetch('/api/auth/sign-in-with-otp', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                email,
                ...(captchaToken && { captchaToken }),
              }),
            });

            const data = await response.json();

            if (data.data.error === null && data.data.user === null && data.data.session === null) {
              // Existing user: confirm signin with OTP
              await handleOTPVerification(email, documentId, uploadResponse);
            } else {
              // New user: create account with random password
              await handleNewUserCreation(email, documentId);
            }
          } catch (error) {
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: 'Something went wrong. Please try again.',
            });
          }
        },
        allowOutsideClick: () => !Swal.isLoading(),
      });
    },
    [captchaContext],
  );

  /**
   * Handle the OTP verification for existing users
   */
  const handleOTPVerification = useCallback(
    async (email: string, documentId: string, uploadResponse: UploadSuccessResponse) => {
      await Swal.fire({
        customClass: {
          inputLabel: 'font-normal text-[14px] leading-[18px] space-y-4 text-[#585858] swal2-label-inline-flex',
          confirmButton:
            'bg-[#F0401D] text-white py-[15px] px-[155px] rounded-[10px] text-[16px] leading-[20px] font-medium swal2-email-validation-btn',
        },
        html: `
        <div style="font-size: 22px; line-height: 26px; font-weight: 500; color: #020202; text-align: center; margin-bottom: 16px;">
          Please enter the 6-digit code sent to your email
        </div>
        <span style="font-weight: 400; font-size: 18px; line-height: 26px; color: #585858">User already exists</span>
      `,
        input: 'number',
        inputLabel: 'Enter 6-digit code',
        inputPlaceholder: '123456',
        showLoaderOnConfirm: true,
        inputValidator: (value) => {
          if (!value) {
            return 'You must enter the 6-digit code sent to your email to download the document.';
          }
        },
        preConfirm: async (token) => {
          try {
            const response = await fetch('/api/auth/verify-otp', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ email, token, type: 'email' }),
            });

            const data = await response.json();

            if (!response.ok) {
              Swal.fire({
                icon: 'error',
                title: 'Error',
                text: data.error || 'Failed to verify OTP',
              });
              return false;
            }

            const { data: userData } = await supabase.auth.getUser();
            const { data: subscriptionData } = await supabase.rpc('has_subscription_status', {
              user_id: userData?.user?.id!,
            });

            const subscription = subscriptionData?.[0];
            const hasSubscription =
              (subscription?.has_subscription || userData?.user?.user_metadata.is_subscribed) ?? false;

            if (!hasSubscription) {
              Swal.close();
              router.push('/checkout');
              return;
            }

            await documentDownload(documentId, uploadResponse.file.file_name || '');
            Swal.close();
          } catch (error) {
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: 'Failed to verify OTP. Please try again.',
            });
          }
        },
        allowOutsideClick: () => !Swal.isLoading(),
      });
    },
    [supabase, router, documentDownload],
  );

  /**
   * Handle the new user creation
   */
  const handleNewUserCreation = useCallback(
    async (email: string, documentId: string) => {
      const randomPassword = generateRandomPassword();

      try {
        const refreshedCaptchaToken = await captchaContext.refreshCaptchaToken();
        await fetch('/api/user/create-user', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email,
            password: randomPassword,
            ...(refreshedCaptchaToken && { captchaToken: refreshedCaptchaToken }),
          }),
        });

        Swal.close();
        router.push(`/checkout?documentId=${documentId}`);
      } catch (error) {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to create your account. Please try again.',
        });
      }
    },
    [captchaContext, router],
  );

  /**
   * Main function that handles the file download
   * Determine if the user is authenticated and applies the correct logic
   */
  const handleDownload = useCallback(
    async (documentId: string, uploadResponse: UploadSuccessResponse) => {
      if (!user || user.is_anonymous) {
        await handleEmailModal(documentId, uploadResponse);
      } else {
        await checkSubscriptionAndDownload(documentId, uploadResponse.file.file_name || '');
      }
    },
    [user, handleEmailModal, checkSubscriptionAndDownload],
  );

  /**
   * Handle Google sign-in for anonymous users
   */
  const handleGoogleSignIn = useCallback(
    async (documentId: string, uploadResponse: UploadSuccessResponse) => {
      try {
        await signInWithProviderAction(
          'google',
          `/auth/sign-in-redirect?documentId=${documentId}&fileName=${uploadResponse.file.file_name}`,
        );
      } catch (error) {
        console.error('Google sign-in error:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to sign in with Google. Please try again.',
        });
      }
    },
    [supabase, router, documentDownload],
  );

  return {
    handleDownload,
    documentDownload,
    checkSubscriptionAndDownload,
  };
}
